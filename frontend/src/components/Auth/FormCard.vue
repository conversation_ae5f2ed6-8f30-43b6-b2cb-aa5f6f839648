<template>
    <div class="w-full max-w-md bg-gray-800 rounded-lg p-8">
        <!-- Debug info -->
        <div class="bg-blue-500 text-white p-2 mb-4">
            FormCard component is rendering! Title: {{ title }}
        </div>

        <!-- Form Title -->
        <h2 class="text-xl text-white mb-6">{{ title }}</h2>

        <!-- Form Content -->
        <form @submit.prevent="$emit('submit')">
            <slot></slot>
        </form>
    </div>
</template>

<script setup>
import { HelpCircleIcon } from 'lucide-vue-next'

defineProps({
    title: String
})

defineEmits(['submit'])
</script>