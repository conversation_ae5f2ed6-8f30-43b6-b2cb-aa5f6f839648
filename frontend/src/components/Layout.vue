<!-- src/components/Auth/Layout.vue -->

<template>
    <div style="background: purple; color: white; padding: 20px; min-height: 100vh;">
        <h2 style="font-size: 20px; margin-bottom: 20px;">DEBUG: Layout component is rendering!</h2>

        <div style="background: orange; color: black; padding: 10px; margin-bottom: 10px;">
            Slot content below:
        </div>

        <slot></slot>

        <!-- Footer -->
        <div style="margin-top: 20px; text-align: center;">
            <p>MythicalFramework 2020 - {{ new Date().getFullYear() }}</p>
        </div>
    </div>
</template>

<style scoped>
.stars {
    position: absolute;
    inset: 0;
    background-image: radial-gradient(2px 2px at calc(random() * 100%) calc(random() * 100%), white, transparent);
    background-size: 200px 200px;
    animation: twinkle 8s infinite;
}

.mountains {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30vh;
    background-image:
        linear-gradient(170deg, transparent 0%, #0a0a1f 80%),
        linear-gradient(150deg, #1a0b2e 0%, transparent 100%);
    clip-path: polygon(0 100%, 20% 65%, 40% 90%, 60% 60%, 80% 85%, 100% 50%, 100% 100%);
}

@keyframes twinkle {

    0%,
    100% {
        opacity: 0.8;
    }

    50% {
        opacity: 0.4;
    }
}
</style>