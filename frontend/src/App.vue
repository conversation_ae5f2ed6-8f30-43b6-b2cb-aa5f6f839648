<template>
  <div style="background: red; color: white; padding: 20px; min-height: 100vh;">
    <h1 style="font-size: 24px; margin-bottom: 20px;">DEBUG: App.vue is rendering!</h1>

    <!-- Debug info -->
    <div v-if="showDebug" style="background: blue; padding: 10px; margin-bottom: 10px;">
      <div>Route: {{ $route.path }}</div>
      <div>Auth Loading: {{ authStore.isLoading }}</div>
      <div>Auth Initialized: {{ authStore._initialized }}</div>
      <div>Is Authenticated: {{ authStore.isAuthenticated }}</div>
      <div>User: {{ authStore.user?.email || 'None' }}</div>
    </div>

    <!-- Test if Vue is working -->
    <div v-if="showDebug" style="background: green; padding: 10px; margin-bottom: 10px;">
      Vue is working! Time: {{ new Date().toLocaleTimeString() }}
    </div>

    <div style="background: yellow; color: black; padding: 10px; margin-bottom: 10px;">
      Router view below:
    </div>

    <router-view></router-view>
  </div>
</template>

<script setup>
import { useAuthStore } from './stores/auth.js'
import { useRoute } from 'vue-router'

const authStore = useAuthStore()
const $route = useRoute()

// Show debug info if VITE_APP_DEBUG is true
const showDebug = import.meta.env.VITE_APP_DEBUG === 'true'

// Log some debug info
console.log('App.vue mounted')
console.log('Current route:', $route.path)
console.log('Auth store state:', {
  isLoading: authStore.isLoading,
  isAuthenticated: authStore.isAuthenticated,
  user: authStore.user,
  _initialized: authStore._initialized
})
</script>
