import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './index.css'
import VueSweetalert2 from 'vue-sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
import { createI18n } from 'vue-i18n'
// import EN from './locale/en.json'
// import { useAuthStore } from './stores/auth.js'

// Temporary: inline locale data to test if JSON import is causing issues
const EN = {
  "auth": {
    "pages": {
      "login": {
        "page": {
          "title": "Login - AveImage Cloud",
          "form": {
            "email": {
              "label": "Email",
              "placeholder": "Enter your email"
            },
            "password": {
              "label": "Password",
              "placeholder": "Enter your password"
            },
            "forgot_password": "Forgot password?",
            "login_button": {
              "label": "Login",
              "loading": "Logging in..."
            },
            "register": {
              "label": "Don't have an account?",
              "link": "Register"
            }
          }
        }
      }
    }
  }
}

console.log('Main.js: Starting app initialization...')

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error caught:', event.error)
    alert('JavaScript Error: ' + event.error.message)
})

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    alert('Promise Rejection: ' + event.reason)
})

try {
    const i18n = createI18n({
        locale: 'EN',
        messages: {
            EN: EN
        }
    })
    console.log('Main.js: i18n created')

    const pinia = createPinia()
    console.log('Main.js: Pinia created')

    const app = createApp(App)
    console.log('Main.js: Vue app created')

    app.use(pinia)
    console.log('Main.js: Pinia installed')

    app.use(router)
    console.log('Main.js: Router installed')

    app.use(VueSweetalert2);
    console.log('Main.js: SweetAlert2 installed')

    app.use(i18n)
    console.log('Main.js: i18n installed')

    // Initialize auth store
    console.log('Main.js: Initializing auth store...')
    const authStore = useAuthStore()
    console.log('Main.js: Auth store created')

    console.log('Main.js: Mounting app...')
    app.mount('#app')
    console.log('Main.js: App mounted successfully')
} catch (error) {
    console.error('Error in main.js:', error)
    alert('Main.js Error: ' + error.message)
}
