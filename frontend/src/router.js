import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import Login from './views/auth/Login.vue'
import Register from './views/auth/Register.vue'
import ForgotPassword from './views/auth/ForgotPassword.vue'
import ResetPassword from './views/auth/ResetPassword.vue'
import OAuthCallback from './views/auth/OAuthCallback.vue'

import NotFound from './views/errors/NotFound.vue'
import Forbidden from './views/errors/Forbidden.vue'
import ServerError from './views/errors/ServerError.vue'

import DashboardHome from './views/Home.vue'
import DashboardAccount from './views/Account.vue'
import Images from './views/Images.vue'
import Upload from './views/Upload.vue'

const routes = [
    // Auth routes
    {
        path: '/auth/login',
        name: 'Login',
        component: Login,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/register',
        name: 'Register',
        component: Register,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/callback',
        name: 'OAuthCallback',
        component: OAuthCallback
    },
    {
        path: '/auth/callback/success',
        name: 'OAuthCallbackSuccess',
        component: OAuthCallback
    },
    {
        path: '/auth/callback/failure',
        name: 'OAuthCallbackFailure',
        component: OAuthCallback
    },
    
    // Dashboard routes
    {
        path: '/',
        redirect: '/dashboard'
    },
    {
        path: '/images',
        redirect: '/dashboard/images'
    },
    {
        path: '/dashboard',
        name: 'Dashboard',
        component: DashboardHome,
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/images',
        name: 'Images',
        component: Images,
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/account',
        name: 'Account',
        component: DashboardAccount,
        meta: { requiresAuth: true }
    },
    {
        path: '/admin',
        name: 'Admin',
        component: () => import('./views/Admin.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/store',
        name: 'Store',
        component: () => import('./views/Store.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/upload',
        name: 'Upload',
        component: Upload,
        meta: { requiresAuth: true }
    },
    
    // Legacy route redirects
    {
        path: '/account',
        redirect: '/dashboard/account'
    },
    
    // Error routes
    {
        path: '/errors/403',
        name: 'Forbidden',
        component: Forbidden
    },
    {
        path: '/errors/500',
        name: 'ServerError',
        component: ServerError
    },
    
    // 404 catch-all
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: NotFound
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
    console.log(`Router: Navigating from ${from.path} to ${to.path}`)
    const authStore = useAuthStore()

    console.log('Router: Auth store state before init:', {
        isLoading: authStore.isLoading,
        isAuthenticated: authStore.isAuthenticated,
        _initialized: authStore._initialized
    })

    // TEMPORARY: Skip auth initialization to test if this is causing the hang
    // Initialize auth state only once on app start
    if (!authStore._initialized) {
        console.log('Router: Skipping auth initialization for debugging...')
        // await authStore.initAuth()
        authStore._initialized = true
        authStore.isLoading = false
        console.log('Router: Auth initialization skipped')
    }

    // Don't redirect if still loading
    if (authStore.isLoading) {
        console.log('Router: Auth still loading, waiting...')
        // Wait a bit for loading to complete
        await new Promise(resolve => setTimeout(resolve, 100))
        if (authStore.isLoading) {
            console.log('Router: Auth still loading after wait, continuing anyway')
            return next() // Continue anyway to prevent infinite loops
        }
    }

    const isAuthenticated = authStore.isAuthenticated && authStore.user && authStore.session
    console.log('Router: Authentication check:', {
        isAuthenticated,
        hasUser: !!authStore.user,
        hasSession: !!authStore.session,
        routeRequiresAuth: to.meta.requiresAuth
    })

    // Check if route requires authentication
    if (to.meta.requiresAuth && !isAuthenticated) {
        console.log('Router: Route requires auth, redirecting to login')
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
    }

    // Check if route requires admin privileges
    if (to.meta.requiresAdmin && isAuthenticated) {
        console.log('Router: Route requires admin, checking privileges...')
        // Import user store to check admin status
        const { useUserStore } = await import('./stores/user.js')
        const userStore = useUserStore()

        // Fetch user profile if not already loaded
        if (!userStore.profile) {
            try {
                console.log('Router: Fetching user profile for admin check...')
                await userStore.fetchProfile()
            } catch (error) {
                console.error('Router: Failed to fetch user profile for admin check:', error)
                next({ name: 'Forbidden' })
                return
            }
        }

        if (!userStore.isAdmin) {
            console.log('Router: User is not admin, redirecting to forbidden')
            next({ name: 'Forbidden' })
            return
        }
        console.log('Router: Admin check passed')
    }

    // Check if route is for guests only (redirect authenticated users)
    if (to.meta.requiresGuest && isAuthenticated) {
        console.log('Router: User is authenticated, redirecting to dashboard')
        next({ name: 'Dashboard' })
        return
    }

    console.log('Router: Navigation allowed, proceeding to route')
    next()
})

export default router